# PLF_Comparison 概率潮流比较程序设计文档

## 1. 程序概述

### 1.1 程序目的
PLF_Comparison.m 是一个概率潮流分析比较程序，用于对比蒙特卡洛法和Gram-Charlier展开法在概率潮流计算中的性能和精度。

### 1.2 主要功能
- 蒙特卡洛概率潮流计算
- Gram-Charlier展开法概率潮流计算
- 两种方法结果对比分析
- 可再生能源随机性建模
- 概率分布可视化

### 1.3 技术特点
- 支持多种电力系统测试算例
- 可配置的可再生能源接入
- 详细的误差分析和统计
- 结果保存和可视化

## 2. 系统架构

### 2.1 程序结构
```
PLF_Comparison.m
├── 0. 基本参数设置
├── 1. 初始化系统参数
├── 2. 蒙特卡洛法概率潮流
├── 3. Gram-Charlier展开法概率潮流
└── 4. 结果对比分析
```

### 2.2 依赖关系
```
PLF_Comparison.m
├── MatPower工具箱
│   ├── loadcase()
│   ├── mpoption()
│   ├── idx_bus()
│   ├── idx_brch()
│   └── bustypes()
├── 自定义函数
│   ├── runpf2()
│   ├── INJECT_POWER()
│   ├── GramCharlierVm2()
│   ├── GramCharlierPf2()
│   ├── b2r()
│   └── r2b()
└── 数据文件
    ├── 测试算例文件
    └── 可再生能源CSV文件
```

## 3. 详细设计

### 3.1 参数配置模块

#### 3.1.1 基本参数
- `N`: 蒙特卡洛仿真次数
- `casedata`: 测试算例名称
- `EDratio`: 标准差与期望值比例
- `ResultSave`: 结果保存标志

#### 3.1.2 可再生能源参数
- `ConnectRE`: 可再生能源接入标志
- `busRE`: 接入节点编号数组
- `REPowerRatio`: 功率调整比例系数
- `RE_CSV_Files`: 数据文件路径数组
- `RE_CSV_Columns`: 读取列号数组

#### 3.1.3 绘图参数
根据不同测试算例自动配置：
- 电压幅值范围和步长
- 功率流范围和步长
- 绘图节点和支路选择

### 3.2 系统初始化模块

#### 3.2.1 常量定义
使用MatPower标准索引常量：
```matlab
[PQ, PV, REF, NONE, BUS_I, BUS_TYPE, PD, QD, GS, BS, BUS_AREA, VM, 
 VA, BASE_KV, ZONE, VMAX, VMIN, LAM_P, LAM_Q, MU_VMAX, MU_VMIN] = idx_bus;
```

#### 3.2.2 系统尺寸确定
- 节点总数 (nb)
- 支路总数 (nl) 
- 投运支路数 (ncl)
- 各类型节点数量 (npv, npq)

### 3.3 蒙特卡洛法模块

#### 3.3.1 负荷抽样
```matlab
for i=1:nb
    EX_P = LoadPQ(i, 1);
    DX_P = abs(EX_P * EDratio);
    if EX_P == 0
        Pload_Sample(:, i) = 0;
    else
        for j = 1:N
            Pload_Sample(j, i) = random('Normal', EX_P, DX_P);
        end
    end
end
```

#### 3.3.2 可再生能源处理
调用 `INJECT_POWER()` 函数获取可再生能源功率数据，并应用到相应节点。

#### 3.3.3 潮流计算循环
```matlab
for k = 1:N
    LoadP = Pload_Sample(k, :)';
    mpc.bus(:, PD) = LoadP;
    LoadQ = Qload_Sample(k, :)';
    mpc.bus(:, QD) = LoadQ;
    results = runpf2(mpc, mpt);
    BusVm_MC(:, k) = results.bus(:, VM);
    BusVa_MC(:, k) = results.bus(:, VA);
    % 存储支路结果...
end
```

### 3.4 Gram-Charlier展开法模块

#### 3.4.1 矩计算初始化
```matlab
order = 8;  % 展开阶数
dW_b = zeros(npv+npq+npq, order);  % 标准正态矩
dW_r = zeros(size(dW_b));          % 原始中心矩
```

#### 3.4.2 负荷矩数据准备
计算负荷的期望和方差，转换为标幺值。

#### 3.4.3 可再生能源矩处理
将可再生能源的原始中心矩添加到负荷功率中。

#### 3.4.4 状态变量和支路矩计算
使用灵敏度矩阵计算状态变量和支路功率的矩。

### 3.5 结果对比分析模块

#### 3.5.1 概率分布计算
- 蒙特卡洛法：基于样本统计
- Gram-Charlier法：解析计算

#### 3.5.2 误差分析
```matlab
Vm_mean_error = abs(BusVm_MC_Mean(pq) - dVm_EX) ./ dVm_EX * 100;
Vm_var_error = abs(BusVm_MC_Var(pq) - dVm_b(pq,2)) ./ dVm_b(pq,2) * 100;
```

#### 3.5.3 可视化输出
- PDF和CDF对比图
- 误差柱状图
- 详细数据表格

## 4. 详细脚本文件说明

### 4.1 主程序文件

#### 4.1.1 PLF_Comparison.m
**功能**: 概率潮流比较分析主程序
**主要模块**:
- 基本参数设置：仿真次数、算例选择、可再生能源配置
- 蒙特卡洛概率潮流计算
- Gram-Charlier展开法概率潮流计算
- 结果对比分析和可视化
- 误差统计和Excel结果保存

**关键参数**:
- `N`: 蒙特卡洛仿真次数 (默认5000)
- `casedata`: 测试算例名称 ('case39', 'case33'等)
- `EDratio`: 负荷标准差与期望值比例 (默认0.1)
- `ConnectRE`: 可再生能源接入标志
- `busRE`: 可再生能源接入节点数组
- `REPowerRatio`: 可再生能源功率调整系数

### 4.2 可再生能源处理模块

#### 4.2.1 INJECT_POWER.m
**功能**: 从CSV文件读取可再生能源数据并进行统计建模
**输入参数**:
- `n`: 数据采样维度
- `csv_file`: CSV文件路径 (如'临城下峪晶澳光伏_96_lstm_预测.csv')
- `data_column`: 读取数据列号 (默认14，对应"偏差值"列)

**输出参数**:
- `INJP_EX`: 注入功率期望值
- `dINJP_r`: 注入功率的八阶累积量
- `INJP_Sample`: 注入功率样本数据

**核心功能**:
- 使用GBK编码读取中文CSV文件
- 计算前8阶原始矩并转换为累积量
- 调用GramCharlierINJP进行概率分布分析
- 输出详细统计信息

#### 4.2.2 GramCharlierINJP.m
**功能**: 基于Gram-Charlier A级数展开计算注入功率的概率分布
**输入参数**:
- `dINJP_b`: 中心矩 (1×8阶)
- `INJP_EX`: 注入功率期望值
- `sample_data`: 样本数据

**输出参数**:
- `pdf`: 概率密度函数
- `cdf`: 累积分布函数
- `xx`: 横坐标

**核心算法**:
- 计算标准化累积量和Gram-Charlier系数
- 使用Hermite多项式展开计算PDF
- 生成对比图表显示统计分布与GC展开的差异
- 保存系数到Excel文件

### 4.3 Gram-Charlier展开计算模块

#### 4.3.1 GramCharlierVm2.m
**功能**: 计算节点电压幅值的Gram-Charlier概率分布
**输入参数**:
- `bi`: 节点电压幅值的中心矩
- `ri`: 节点电压幅值的累积量
- `EX`: 节点电压期望值

**输出参数**:
- `pdf`: 概率密度函数矩阵
- `cdf`: 累积分布函数矩阵
- `xx`: 横坐标矩阵

**自适应参数**:
- 根据系统电压等级自动调整计算范围
- 高压系统: scale=1e-4, 范围[-0.15, 0.15]
- 低压系统: scale=1e-5, 范围[-0.05, 0.05]

#### 4.3.2 GramCharlierPf2.m
**功能**: 计算支路有功功率的Gram-Charlier概率分布
**输入参数**:
- `bi`: 支路功率的中心矩
- `ri`: 支路功率的累积量
- `EX`: 支路功率期望值

**输出参数**:
- `pdf`: 概率密度函数矩阵
- `cdf`: 累积分布函数矩阵
- `xx`: 横坐标矩阵

**自适应参数**:
- 小系统 (<100MW): scale=0.002, 范围[-5, 5]
- 中等系统 (<400MW): scale=0.05, 范围[-50, 50]
- 大系统 (≥400MW): scale=0.5, 范围[-700, 700]

### 4.4 矩转换工具模块

#### 4.4.1 b2r.m
**功能**: 中心矩转换为累积量
**数学原理**: 基于累积量生成函数的性质
**转换公式**:
- r? = b?
- r? = b?
- r? = b? - 3b??
- r? = b? - 10b?b?
- r? = b? - 15b?b? - 10b?? + 30b??
- r? = b? - 21b?b? - 35b?b? + 210b?b??
- r? = b? - 28b?b? - 56b?b? - 35b?? + 420b?b?? + 560b??b? - 630b??

#### 4.4.2 r2b.m
**功能**: 累积量转换为中心矩
**数学原理**: b2r的逆变换
**转换公式**:
- b? = r?
- b? = r?
- b? = r? + 3r??
- b? = r? + 10r?r?
- b? = r? + 15r?r? + 10r?? + 15r??
- b? = r? + 21r?r? + 35r?r? + 105r?r??
- b? = r? + 28r?r? + 56r?r? + 35r?? + 210r?r?? + 280r??r? + 105r??

#### 4.4.3 a2b.m
**功能**: 原始矩转换为中心矩
**应用**: 在INJECT_POWER中用于处理可再生能源数据

### 4.5 潮流计算核心模块

#### 4.5.1 runpf2.m
**功能**: 执行交流潮流计算 (MatPower修改版)
**位置**: 01_PF/01-顶层仿真函数/
**输入参数**:
- `casedata`: MatPower算例结构或文件名
- `mpopt`: MatPower选项结构
- `fname`: 输出文件名 (可选)
- `solvedcase`: 保存求解结果文件名 (可选)

**输出参数**:
- `MVAbase`: 基准功率
- `bus`: 节点数据 (含求解后电压)
- `gen`: 发电机数据
- `branch`: 支路数据 (含求解后潮流)
- `success`: 收敛标志
- `et`: 计算耗时

**核心特性**:
- 默认使用牛顿-拉夫逊法
- 支持PV节点无功越限处理
- 提供详细的收敛信息和计算统计

### 4.6 系统算例文件

#### 4.6.1 测试算例 (06-Case目录)
**case5.m**: 5节点测试系统
**case9.m**: IEEE 9节点系统
**case14.m**: IEEE 14节点系统
**case16.m**: 16节点配电系统
**case33.m**: IEEE 33节点配电系统
**case39.m**: 新英格兰39节点系统
**case69.m**: IEEE 69节点配电系统

**case39系统特点**:
- 39个节点，46条支路，10台发电机
- 包含详细的发电机成本数据
- 电压限制: Vmax=1.06, Vmin=0.94
- 适用于大型系统概率潮流分析

**case33系统特点**:
- 33个节点配电系统
- 适用于配电网概率潮流分析
- 包含详细的负荷和网络参数

### 4.7 辅助功能模块

#### 4.7.1 索引命名函数 (01_PF/02-索引命名函数/)
**idx_bus.m**: 定义节点数据矩阵的列索引常量
**idx_brch.m**: 定义支路数据矩阵的列索引常量
**idx_gen.m**: 定义发电机数据矩阵的列索引常量
**idx_cost.m**: 定义成本数据矩阵的列索引常量

#### 4.7.2 输入输出函数 (01_PF/03-输入输出函数/)
**loadcase.m**: 加载MatPower算例文件
**mpoption.m**: 设置MatPower计算选项
**savecase.m**: 保存计算结果为算例文件
**printpf.m**: 打印潮流计算结果

#### 4.7.3 矩阵构建函数 (01_PF/06-矩阵构建函数/)
**makeYbus2.m**: 构建节点导纳矩阵
**makeSbus.m**: 构建节点注入功率向量
**makeB2.m**: 构建快速解耦法系数矩阵
**makeBdc.m**: 构建直流潮流系数矩阵

#### 4.7.4 潮流求解函数 (01_PF/07-潮流计算函数/)
**newtonpf2.m**: 牛顿-拉夫逊潮流计算
**fdpf2.m**: 快速解耦潮流计算
**gausspf2.m**: 高斯-赛德尔潮流计算
**dcpf.m**: 直流潮流计算

### 4.8 数据文件

#### 4.8.1 可再生能源数据文件
**临城下峪晶澳光伏_96_lstm_预测.csv**: 光伏功率预测数据
**枣强八里庄光伏_96_预测.csv**: 光伏功率预测数据
**磁县池上光伏_96_lstm_预测.csv**: 光伏功率预测数据

**数据格式**:
- 包含96个时间点的功率预测值
- 第14列为"偏差值"，用于概率建模
- 使用GBK编码存储中文标题

#### 4.8.2 结果输出文件
**GCCoef_INJP.xlsx**: 注入功率Gram-Charlier系数
**GCCoef_PF.xlsx**: 支路功率Gram-Charlier系数

## 5. 程序执行流程详解

### 5.1 主程序执行流程
```
PLF_Comparison.m 执行流程:
├── 1. 参数初始化
│   ├── 基本参数设置 (N, casedata, EDratio等)
│   ├── 可再生能源配置 (ConnectRE, busRE, REPowerRatio等)
│   └── 绘图参数自适应配置
├── 2. 系统数据加载
│   ├── 调用loadcase()加载测试算例
│   ├── 提取系统规模信息 (nb, nl, npv, npq)
│   └── 初始化MatPower常量索引
├── 3. 蒙特卡洛概率潮流
│   ├── 负荷正态分布抽样
│   ├── 可再生能源数据处理 (调用INJECT_POWER)
│   ├── N次潮流计算循环 (调用runpf2)
│   └── 统计结果存储
├── 4. Gram-Charlier展开法
│   ├── 负荷矩数据准备
│   ├── 可再生能源矩数据集成
│   ├── 灵敏度矩阵计算
│   ├── 状态变量矩传播
│   └── 支路功率矩计算
├── 5. 结果对比分析
│   ├── 概率分布计算 (调用GramCharlierVm2, GramCharlierPf2)
│   ├── 误差统计分析
│   ├── 可视化图表生成
│   └── Excel结果保存
```

### 5.2 可再生能源数据处理流程
```
INJECT_POWER.m 处理流程:
├── 1. CSV文件读取
│   ├── 设置GBK编码
│   ├── 检测导入选项
│   └── 读取指定列数据
├── 2. 数据预处理
│   ├── 数据长度检查
│   ├── 循环补充不足数据
│   └── 样本数据准备
├── 3. 统计矩计算
│   ├── 计算前8阶原始矩
│   ├── 原始矩转中心矩 (a2b)
│   └── 中心矩转累积量 (b2r)
├── 4. 概率分布分析
│   ├── 调用GramCharlierINJP
│   ├── 生成PDF/CDF对比图
│   └── 保存系数到Excel
```

### 5.3 Gram-Charlier展开计算流程
```
GramCharlier计算流程:
├── 1. 参数初始化
│   ├── 计算标准差 DX = √b?
│   ├── 自适应设置计算范围
│   └── 初始化矩阵
├── 2. 标准化累积量计算
│   ├── z? = b?/DX?, z? = b?/DX?, ...
│   └── 计算到8阶
├── 3. Gram-Charlier系数计算
│   ├── k? = z?/3!, k? = (z?-3)/4!, ...
│   └── 使用递推公式到8阶
├── 4. 概率密度函数计算
│   ├── 计算Hermite多项式 h?, h?, ..., h?
│   ├── 标准正态分布 nd = exp(-x?/2)/√(2π)
│   └── PDF = nd×(1+k?h?+k?h?+...+k?h?)/DX
├── 5. 累积分布函数
│   └── CDF = cumsum(PDF)×scale
```

## 6. 数据结构设计

### 6.1 主要数据结构

#### 6.1.1 系统数据结构 (mpc)
```matlab
mpc.version = '2';           % MatPower版本
mpc.baseMVA = 100;          % 基准功率
mpc.bus = [...];            % 节点数据矩阵 [nb×21]
mpc.gen = [...];            % 发电机数据矩阵 [ng×25]
mpc.branch = [...];         % 支路数据矩阵 [nl×17]
mpc.gencost = [...];        % 发电成本数据矩阵 [ng×7]
```

#### 6.1.2 蒙特卡洛结果数据结构
```matlab
BusVm_MC = zeros(nb, N);        % 节点电压幅值 [节点×样本]
BusVa_MC = zeros(nb, N);        % 节点电压相角 [节点×样本]
BranchPf_MC = zeros(ncl, N);    % 支路有功功率 [支路×样本]
BranchQf_MC = zeros(ncl, N);    % 支路无功功率 [支路×样本]
```

#### 6.1.3 矩数据结构
```matlab
dW_b = zeros(npv+npq+npq, 8);   % 标准正态矩 [状态变量×阶数]
dW_r = zeros(size(dW_b));       % 原始中心矩
dVm_b = zeros(npq, 8);          % 电压幅值中心矩
dPf_b = zeros(ncl, 8);          % 支路功率中心矩
```

### 6.2 关键变量说明

#### 6.2.1 系统规模变量
- `nb`: 节点总数
- `nl`: 支路总数
- `ncl`: 投运支路数
- `npv`: PV节点数量
- `npq`: PQ节点数量
- `ng`: 发电机数量

#### 6.2.2 索引变量 (来自idx_bus, idx_brch)
```matlab
% 节点数据列索引
[PQ, PV, REF, NONE, BUS_I, BUS_TYPE, PD, QD, GS, BS,
 BUS_AREA, VM, VA, BASE_KV, ZONE, VMAX, VMIN, LAM_P,
 LAM_Q, MU_VMAX, MU_VMIN] = idx_bus;

% 支路数据列索引
[F_BUS, T_BUS, BR_R, BR_X, BR_B, RATE_A, RATE_B, RATE_C,
 TAP, SHIFT, BR_STATUS, PF, QF, PT, QT, MU_SF, MU_ST,
 ANGMIN, ANGMAX, MU_ANGMIN, MU_ANGMAX] = idx_brch;
```

## 7. 算法实现细节

### 7.1 蒙特卡洛抽样算法
```matlab
% 负荷正态分布抽样
for i = 1:nb
    EX_P = LoadPQ(i, 1);                    % 期望值
    DX_P = abs(EX_P * EDratio);             % 标准差
    if EX_P == 0
        Pload_Sample(:, i) = 0;
    else
        for j = 1:N
            Pload_Sample(j, i) = random('Normal', EX_P, DX_P);
        end
    end
end
```

### 7.2 灵敏度矩阵计算
```matlab
% 雅可比矩阵分块
J11 = dSbus_dVa(range_Va, range_Va);       % ?P/?θ
J12 = dSbus_dVm(range_Va, range_Vm);       % ?P/?V
J21 = dSbus_dVa(range_Vm, range_Va);       % ?Q/?θ
J22 = dSbus_dVm(range_Vm, range_Vm);       % ?Q/?V

% 灵敏度矩阵
S = -inv([J11, J12; J21, J22]);            % 状态变量灵敏度
```

### 7.3 矩传播算法
```matlab
% 状态变量矩计算
for r = 1:8
    dW_b(:, r) = S * dPQ_b(:, r);          % 线性传播
end

% 支路功率矩计算
for r = 1:8
    dPf_b(:, r) = dSf_dVa * dVa_b(:, r) + dSf_dVm * dVm_b(:, r);
end
```

## 8. 错误处理与异常管理

### 8.1 文件读取错误处理
```matlab
% CSV文件读取保护
try
    opts = detectImportOptions(csv_file, 'Encoding', 'GBK');
    M = readtable(csv_file, opts);
catch
    warning('CSV文件读取失败，使用默认数据');
    % 使用默认数据或退出
end
```

### 8.2 潮流计算收敛性检查
```matlab
% 潮流计算结果验证
results = runpf2(mpc, mpt);
if ~results.success
    warning('第%d次潮流计算不收敛', k);
    % 记录失败次数或采用备用策略
end
```

### 8.3 数值稳定性保护
```matlab
% PDF负值保护
pdf(pdf < 0) = 0;

% 矩阵奇异性检查
if rcond(J) < 1e-12
    warning('雅可比矩阵接近奇异');
end
```

## 9. 性能优化策略

### 9.1 内存优化
```matlab
% 预分配大型矩阵
BusVm_MC = zeros(nb, N);
BusVa_MC = zeros(nb, N);
BranchPf_MC = zeros(ncl, N);

% 及时清理临时变量
clear temp_matrix large_array;
```

### 9.2 计算优化
```matlab
% 向量化操作替代循环
Pload_Sample = repmat(EX_P', N, 1) + randn(N, nb) .* repmat(DX_P', N, 1);

% 矩阵运算优化
dW_b = S * dPQ_b;  % 替代逐列计算
```

### 9.3 并行计算潜力
- 蒙特卡洛样本计算可并行化
- 多个节点/支路的概率分布计算可并行
- 不同算例的对比分析可并行

## 10. 扩展性与维护性

### 10.1 新算例添加
```matlab
% 在参数配置段添加新算例
elseif strcmp(casedata, 'case118')
    scaleVm = 1e-5;
    lowerVm = -0.1;
    upperVm = 0.1;
    % 其他参数...
end
```

### 10.2 新概率方法集成
- 遵循标准接口: `[pdf, cdf, xx] = NewMethod(moments, EX)`
- 在主程序中添加方法选择开关
- 保持结果数据结构一致性

### 10.3 代码模块化改进建议
- 将参数配置独立为配置文件
- 将绘图功能封装为独立函数
- 将误差分析功能模块化
- 增加日志记录功能

## 11. 独立功能模块详解

### 11.1 蒙特卡洛专用模块 (01-MonteCarlo目录)

#### 11.1.1 PLF_MC.m
**功能**: 独立的蒙特卡洛概率潮流程序
**特点**:
- 专门用于蒙特卡洛方法的概率潮流分析
- 不依赖Gram-Charlier展开法
- 可独立运行和验证

#### 11.1.2 负荷抽样模块 (Sample子目录)
**LoadSample_Excel.m**: 从Excel文件读取负荷样本数据
**LoadSample_MPC.m**: 从MatPower算例生成负荷样本
**PLoadSample.m**: 有功负荷抽样函数
**PLF_Load_V1_150606A.m**: 负荷概率潮流版本A
**PLF_Load_V1_150606B.m**: 负荷概率潮流版本B

### 11.2 Gram-Charlier专用模块 (02-GramCharlier目录)

#### 11.2.1 PLF_GC.m
**功能**: 独立的Gram-Charlier概率潮流程序
**核心特性**:
- 基于累积量和Gram-Charlier展开的概率潮流
- 支持正态分布负荷建模
- 不包含分布式电源(DG)模型
- 输出PQ节点电压分布和支路功率分布

**主要算法流程**:
```matlab
% 1. 系统初始化
mpc = loadcase(casedata);
[nb, nl, npv, npq] = 系统规模确定;

% 2. 负荷矩数据准备
dPQ_b = 负荷中心矩计算;
dPQ_r = b2r(dPQ_b);  % 转换为累积量

% 3. 灵敏度矩阵计算
[J11, J12, J21, J22] = 雅可比矩阵分块;
S = -inv([J11, J12; J21, J22]);

% 4. 状态变量矩传播
dW_b = S * dPQ_b;  % 线性传播

% 5. 节点电压和支路功率矩计算
dVm_b = 电压幅值矩提取;
dPf_b = 支路功率矩计算;
```

#### 11.2.2 专用Gram-Charlier函数
**GramCharlierVm.m**: 节点电压Gram-Charlier展开 (旧版本)
**GramCharlierPf.m**: 支路功率Gram-Charlier展开 (旧版本)
**GramCharlierWT.m**: 风电功率Gram-Charlier展开
**GramCharlierWT2.m**: 风电功率Gram-Charlier展开 (改进版)
**GramCharlierPV.m**: 光伏功率Gram-Charlier展开

#### 11.2.3 可再生能源专用模块
**WT_GC.m**: 风电Gram-Charlier概率潮流
**PV_GC.m**: 光伏Gram-Charlier概率潮流

**功能特点**:
- 专门处理风电/光伏的概率特性
- 考虑可再生能源的非正态分布特征
- 集成到概率潮流计算框架

### 11.3 离散模型概率潮流模块

#### 11.3.1 Main_DM.m
**功能**: 离散模型概率潮流主程序
**特点**:
- 使用离散概率分布建模
- 适用于有限状态的随机变量
- 提供与连续模型的对比分析

#### 11.3.2 PLF_DM.m
**功能**: 离散模型概率潮流核心算法
**算法原理**:
- 将连续随机变量离散化为有限状态
- 使用状态概率进行潮流计算
- 通过状态组合获得系统概率分布

#### 11.3.3 离散模型专用函数
**GramCharlierVmDM.m**: 离散模型节点电压Gram-Charlier展开
**GramCharlierPfDM.m**: 离散模型支路功率Gram-Charlier展开
**WGramCharlierPfDM.m**: 加权离散模型支路功率展开

#### 11.3.4 结果处理模块
**DataView.m**: 数据查看和预处理工具
**DgResultsPlot.m**: 离散模型结果绘图工具

**功能特性**:
- 专门的离散概率分布可视化
- 状态概率柱状图显示
- 与连续分布的对比分析

### 11.4 矩转换工具完整集合 (03-Convert目录)

#### 11.4.1 a2b.m
**功能**: 原始矩转换为中心矩
**数学公式**:
- b? = 0 (中心矩定义)
- b? = a? - a??
- b? = a? - 3a?a? + 2a??
- b? = a? - 4a?a? + 6a?a?? - 3a??
- ... (递推到8阶)

#### 11.4.2 a2r.m
**功能**: 原始矩转换为累积量
**实现方式**: a → b → r (两步转换)

#### 11.4.3 betaMoment.m
**功能**: Beta分布的矩计算
**应用场景**:
- 可再生能源出力的Beta分布建模
- 提供Beta分布的前8阶矩
- 支持不同参数的Beta分布

**数学原理**:
```matlab
% Beta分布矩计算
% B(α,β)分布的第k阶原始矩
% E[X^k] = Γ(α+k)Γ(α+β) / [Γ(α)Γ(α+β+k)]
```

## 12. 程序配置与参数说明

### 12.1 系统算例配置参数

#### 12.1.1 case33配置
```matlab
% 配电系统参数
scaleVm = 5e-3;          % 电压计算步长
lowerVm = -1;            % 电压下限 (标幺值)
upperVm = 1;             % 电压上限 (标幺值)
scalePf = 5e-2;          % 功率计算步长
lowerPf = -10;           % 功率下限 (MW)
upperPf = 10;            % 功率上限 (MW)
```

#### 12.1.2 case39配置
```matlab
% 输电系统参数
scaleVm = 5e-5;          % 更精细的电压步长
lowerVm = -0.15;         % 更宽的电压范围
upperVm = 0.15;
scalePf = 5e-1;          % 更大的功率步长
lowerPf = -2000;         % 更大的功率范围 (MW)
upperPf = 1000;
```

### 12.2 可再生能源配置

#### 12.2.1 接入节点配置
```matlab
busRE = [18, 22, 30];              % 接入节点编号
REPowerRatio = [1.0, 1.0, 1.0];    % 功率缩放系数
```

#### 12.2.2 数据文件配置
```matlab
RE_CSV_Files = {
    '临城下峪晶澳光伏_96_lstm_预测.csv',
    '枣强八里庄光伏_96_预测.csv',
    '磁县池上光伏_96_lstm_预测.csv'
};
RE_CSV_Columns = [14, 14, 14];     % 读取第14列(偏差值)
```

### 12.3 计算精度控制

#### 12.3.1 蒙特卡洛参数
```matlab
N = 5000;                % 仿真次数 (可调整)
EDratio = 0.1;           % 负荷不确定性水平
```

#### 12.3.2 Gram-Charlier展开阶数
```matlab
order = 8;               % 展开到8阶
nMoments = 8;            % 计算前8阶矩
```

## 13. 结果输出与可视化

### 13.1 图表输出类型

#### 13.1.1 概率分布对比图
- PDF对比: 蒙特卡洛直方图 vs Gram-Charlier曲线
- CDF对比: 经验分布函数 vs 解析分布函数
- 误差分析: 均值误差和方差误差柱状图

#### 13.1.2 系统状态分布图
- 节点电压幅值概率分布
- 支路有功功率概率分布
- 可再生能源出力概率分布

### 13.2 数据保存格式

#### 13.2.1 Excel文件输出
**GCCoef_INJP.xlsx**: 注入功率Gram-Charlier系数
- 期望值、标准差、偏度、峰度

**GCCoef_PF.xlsx**: 支路功率Gram-Charlier系数
- 各支路的展开系数矩阵

#### 13.2.2 MAT文件保存
```matlab
if ResultSave == 1
    save(['PLF_Results_', casedata, '.mat'], ...
         'BusVm_MC', 'BranchPf_MC', 'dVm_b', 'dPf_b');
end
```

## 14. 程序验证与测试

### 14.1 算法验证方法
- 蒙特卡洛结果作为基准
- Gram-Charlier展开精度验证
- 不同算例的一致性检验

### 14.2 误差评估指标
```matlab
% 均值相对误差
mean_error = abs(MC_mean - GC_mean) ./ MC_mean * 100;

% 方差相对误差
var_error = abs(MC_var - GC_var) ./ MC_var * 100;

% 分布距离度量 (可选)
KS_distance = kstest2(MC_samples, GC_samples);
```

### 14.3 性能基准测试
- 计算时间对比: 蒙特卡洛 vs Gram-Charlier
- 内存使用量监控
- 不同系统规模的扩展性测试

## 15. 总结

本概率潮流比较程序提供了完整的概率潮流分析框架，包含：

1. **完整的算法实现**: 蒙特卡洛法和Gram-Charlier展开法
2. **丰富的功能模块**: 可再生能源建模、矩转换、概率分布计算
3. **多样的测试算例**: 从5节点到69节点的各种电力系统
4. **详细的结果分析**: 误差统计、可视化对比、数据保存
5. **良好的扩展性**: 模块化设计便于添加新方法和新算例

程序设计遵循了软件工程的最佳实践，具有良好的可读性、可维护性和可扩展性，为概率潮流分析研究提供了可靠的工具平台。