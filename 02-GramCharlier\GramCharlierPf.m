function [pdf, cdf, xx] = GramCharlierPf(bi, ri, EX)
% GramCharlierPf  支路功率概率分布计算
% Copyright (c) by <PERSON><PERSON><PERSON><PERSON> Li, School of Electrical Engineering, Beijing Jiaotong University
% Last Modified Date：2015-11-03
%% 第1部分：程序说明
% 基于Gram-Charlier级数展开计算支路功率的概率密度函数和累积分布函数
% 输出参数：pdf为概率密度函数，cdf为累积分布函数，xx为横坐标（x轴）
% 输入参数：bi为中心矩矩阵，ri为累积量矩阵，EX为支路功率期望值
%% 第2部分：依赖函数（无外部依赖）
%% 第3部分：注意事项
% 1：支路功率可正可负
% 2：根据case规模自动调整参数
% 3：使用标准化变量计算
% 4：注意bi矩阵的维度

%% 参数设置
n = size(bi,1);                                                             % 支路数量或节点数量
DX = sqrt(bi(:,2));                                                         % 标准差 （二阶中心矩的平方根）

% 根据系统规模自动调整
max_branch_power = max(abs(EX));

% 根据功率大小自动调整参数
if max_branch_power < 100  % 小系统如case33、case14等
    scale = 0.002;                                                          % x轴步长，0.001 for case33、case14，大系统0.05
    lowerX = -5;                                                            % dX倍数下限-5 for case33、case14，大系统-50
    upperX = 5;                                                             % dX倍数上限5 for case33、case14，大系统50
elseif max_branch_power < 400  % 中等系统
    scale = 0.05;                                                           % 中等步长
    lowerX = -50;                                                           % 中等下限
    upperX = 50;                                                            % 中等上限
else  % 大系统如case39等
    scale = 0.5;                                                            % 大系统步长
    lowerX = -700;                                                          % 大系统下限
    upperX = 700;                                                           % 大系统上限
end

%% 初始化
g = zeros(size(bi));                                                        % 标准化累积量
c = zeros(size(bi));                                                        % Gram-Charlier系数

%% 标准化累积量（去量纲化处理）
g(:,3) = ri(:,3)./(DX.^3);
g(:,4) = ri(:,4)./(DX.^4);
g(:,5) = ri(:,5)./(DX.^5);
g(:,6) = ri(:,6)./(DX.^6);

%% Gram-Charlier系数计算
c(:,3) = (g(:,3)) ./ factorial(3);
c(:,4) = (g(:,4)) ./ factorial(4);
c(:,5) = (g(:,5)) ./ factorial(5);
c(:,6) = (g(:,6) + 10.*g(:,3).^2) ./ factorial(6);

%% pdf和cdf计算
for j = 1:n                                                                 % 第j个支路或节点
    i = 0;                                                                  % 横坐标点计数器
    for xx = lowerX:scale:upperX                                            % 功率范围for case33
        i = i + 1;
        x = xx/DX(j);

        % 标准化Hermite多项式
        h3 = x^3 -  3*x;
        h4 = x^4 -  6*x^2 +  3;
        h5 = x^5 - 10*x^3 +  15*x;
        h6 = x^6 - 15*x^4 +  45*x^2 - 15;

        % 标准正态分布（normal distribution）
        nd = exp(-x^2/2) / sqrt(2*pi);

        % Gram-Charlier级数展开
        pdf_temp = nd*(1+c(j,3)*h3+c(j,4)*h4+c(j,5)*h5+c(j,6)*h6);
        pdf(i,j) = pdf_temp *scale / DX(j);
    end
end
cdf = cumsum(pdf);                                                          % 累积分布函数

%% 横坐标生成
xx = (lowerX:scale:upperX)';                                                % 功率范围for case33，标准化坐标
xx = repmat(xx,1,n);                                                        % 复制为n列
for j = 1:n
    xx(:,j) = xx(:,j) + EX(j);                                              % 加偏移
end

end