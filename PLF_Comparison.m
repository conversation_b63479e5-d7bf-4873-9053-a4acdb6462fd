% PLF_Comparison  概率潮流对比程序：蒙特卡洛法与Gram-Charlier级数展开法
% Copyright (c) by <PERSON><PERSON><PERSON><PERSON> Li, School of Electrical Engineering, Beijing Jiaotong University

clear;
clc;

tic;
%% ###############################################################
%% 0. 参数设置 - 仿真参数和可再生能源配置
% 基本仿真参数
N = 5000;                                                                 % 蒙特卡洛采样次数
casedata = 'case39';                                                       % 测试系统算例
ResultSave = 1;                                                            % 结果保存标志
EDratio = 0.1;                                                             % DX = 0.1 * EX (负荷扰动标准差比例)

% 可再生能源配置
ConnectRE = 1;                                                             % 可再生能源接入标志
busRE = [18, 22, 30];                                                      % 可再生能源接入节点
REPowerRatio = [1.0, 1.0, 1.0];                                            % 可再生能源功率缩放比例

% 可再生能源数据文件配置
RE_CSV_Files = {'磁县池上光伏_96_lstm_预测.csv', '枣强八里庄光伏_96_预测.csv', '临城下峪晶澳光伏_96_lstm_预测.csv'};  % 可再生能源CSV文件
RE_CSV_Columns = [14, 14, 14];                                             % 各CSV文件功率数据列号

% 绘图参数配置
if strcmp(casedata, 'case33')
    % case33 系统参数
    scaleVm = 5e-3;                                                        % Vm轴x步长
    lowerVm = -1;                                                          % dX倍数下限范围
    upperVm = 1;                                                           % dX倍数上限范围
    scalePf = 5e-2;                                                        % Pf轴x步长
    lowerPf = -10;                                                         % dX倍数下限范围
    upperPf = 10;                                                          % dX倍数上限范围
elseif strcmp(casedata, 'case39')
    % case39 系统参数(扩大范围)
    scaleVm = 5e-5;                                                        % Vm轴x步长
    lowerVm = -0.15;                                                       % dX倍数下限范围 (扩大范围)
    upperVm = 0.15;                                                        % dX倍数上限范围 (扩大范围)
    scalePf = 5e-1;                                                        % Pf轴x步长 (增大步长)
    lowerPf = -2000;                                                       % dX倍数下限范围 (扩大范围)
    upperPf = 1000;                                                        % dX倍数上限范围 (扩大范围)
else
    % 其他系统默认参数
    scaleVm = 5e-5;                                                        % Vm轴x步长
    lowerVm = -0.075;                                                      % dX倍数下限范围
    upperVm = 0.075;                                                       % dX倍数上限范围
    scalePf = 5e-3;                                                        % Pf轴x步长
    lowerPf = -5;                                                          % dX倍数下限范围
    upperPf = 5;                                                           % dX倍数上限范围
end

% 绘图节点和支路选择
nodes_to_plot = [1, 10, 20, 29];                                           % 需要绘图的PQ节点
branches_to_plot = [1, 2, 3, 4];                                           % 需要绘图的支路

%% ###############################################################
%% 1. 系统数据读取
%% 定义索引常量
[PQ, PV, REF, NONE, BUS_I, BUS_TYPE, PD, QD, GS, BS, BUS_AREA, VM, ...
	VA, BASE_KV, ZONE, VMAX, VMIN, LAM_P, LAM_Q, MU_VMAX, MU_VMIN] = idx_bus;

[F_BUS, T_BUS, BR_R, BR_X, BR_B, RATE_A, RATE_B, RATE_C, ...
    TAP, SHIFT, BR_STATUS, PF, QF, PT, QT, MU_SF, MU_ST, ...
    ANGMIN, ANGMAX, MU_ANGMIN, MU_ANGMAX] = idx_brch;

%% 读取系统数据
mpc = loadcase(casedata);                                                  % 加载测试系统
mpt = mpoption('out.all', 0, 'verbose', 0);                                % MatPower选项设置

%% 系统参数
baseMVA = mpc.baseMVA;                                                     % 基准功率
nb = size(mpc.bus, 1);                                                     % 节点数量
nl = size(mpc.branch, 1);                                                  % 支路数量
ncl = sum(mpc.branch(:,BR_STATUS));                                        % 在线支路数量
[ref, pv, pq] = bustypes(mpc.bus, mpc.gen);                                % 节点类型分类
npv = length(pv);                                                          % PV节点数量
npq = length(pq);                                                          % PQ节点数量

%% ###############################################################
%% 2. 蒙特卡洛概率潮流计算
fprintf('正在进行蒙特卡洛概率潮流计算...\n');

%% 基础潮流计算
results_mc = runpf2(mpc, mpt);                                             % 基础潮流计算
bus_mc     = results_mc.bus;
branch_mc  = results_mc.branch;
gen_mc     = results_mc.gen;
BusVm_EX = bus_mc(:,VM);                                                   % 节点电压期望
BusVa_EX = bus_mc(:,VA);
Pf_EX = branch_mc(:,PF);                                                   % 支路有功功率
Qf_EX = branch_mc(:,QF);                                                   % 支路无功功率
Pt_EX = branch_mc(:,PT);                                                   % 支路有功功率
Qt_EX = branch_mc(:,QT);                                                   % 支路无功功率

%% 负荷随机抽样
LoadPQ = mpc.bus(:, PD:QD);                                                % 负荷有功无功数据
Pload_Sample = zeros(N, nb);                                               % 有功负荷抽样矩阵
Qload_Sample = zeros(N, nb);                                               % 无功负荷抽样矩阵
for i=1:nb                                                                 % i表示第i个节点
    % 有功负荷抽样
    EX_P = LoadPQ(i, 1);                                                   % 有功负荷期望值（实际值，非标幺值）
    DX_P = abs(EX_P * EDratio);                                            % 有功负荷标准差DX（实际值）
    if EX_P == 0                                                           % 如果该节点有功负荷为0
        Pload_Sample(:, i) = 0;
    else
        for j = 1:N
            Pload_Sample(j, i) = random('Normal', EX_P, DX_P);             % 正态分布抽样N(EX_P, DX_P)，实际值
        end
    end
    % 无功负荷抽样
    EX_Q = LoadPQ(i, 2);
    DX_Q = abs(EX_Q * EDratio);
    if EX_Q == 0
        Qload_Sample(:, i) = 0;
    else
        for j = 1:N
            Qload_Sample(j, i) = random('Normal', EX_Q, DX_Q);
        end
    end
end

%% ????????????????
if ConnectRE == 1
    % ???????????????
    num_RE = length(busRE);
    if length(REPowerRatio) ~= num_RE || length(RE_CSV_Files) ~= num_RE || length(RE_CSV_Columns) ~= num_RE
        error('????????????????ò????: busRE, REPowerRatio, RE_CSV_Files, RE_CSV_Columns');
    end

    n_samples = ceil(N / 96);  % ?????????96С?????????????????????????N?η???

    % ???????????????????
    for i = 1:num_RE
        % ????INJECT_POWER?????????????????????CSV??????к??
        [INJP_EX, ~, INJP_Sample] = INJECT_POWER(n_samples, RE_CSV_Files{i}, RE_CSV_Columns(i));

        % ???????????
        if length(INJP_Sample) < N
            INJP_Sample = repmat(INJP_Sample, ceil(N/length(INJP_Sample)), 1);
        end

        % ??N??????????ù?????????
        PRE_Sample = INJP_Sample(1:N) * REPowerRatio(i);

        % ?????????????????й??????м??
        fprintf('??????????????? %d, ???????: %.2f MW???????й??????\n', busRE(i), mean(PRE_Sample));
        Pload_Sample(:, busRE(i)) = Pload_Sample(:, busRE(i)) - PRE_Sample;  % ?й???????? (????????????)
        % ??????????????
    end
end

%% N?γ??????????
% ?????洢????
BusVm_MC = zeros(nb, N);
BusVa_MC = zeros(nb, N);
Pf_MC = zeros(ncl, N);                                                     % ?洢?????·?й?
Qf_MC = zeros(ncl, N);                                                     % ?洢?????·???
Pt_MC = zeros(ncl, N);                                                     % ?洢?????·?й?
Qt_MC = zeros(ncl, N);                                                     % ?洢?????·???

% ????????·????
cl_idx = find(mpc.branch(:,BR_STATUS) == 1);

% N?γ??????????
for k = 1:N                                                                % k??????k?γ???????
    LoadP = Pload_Sample(k, :);                                            % ?????k???й??????????????????????????
    LoadP = LoadP';                                                        % ????????
    mpc.bus(:, PD) = LoadP;                                                % ????bus??????й???????
    LoadQ = Qload_Sample(k, :);
    LoadQ = LoadQ';
    mpc.bus(:, QD) = LoadQ;                                                % ????bus??????????????
    results = runpf2(mpc, mpt);                                            % ???-?????????????
    BusVm_MC(:, k) = results.bus(:, VM);                                   % ?洢??k?ν???????
    BusVa_MC(:, k) = results.bus(:, VA);                                   % ?洢???
    % ?洢?????·????
    Pf_MC(:, k) = results.branch(cl_idx, PF);
    Qf_MC(:, k) = results.branch(cl_idx, QF);
    Pt_MC(:, k) = results.branch(cl_idx, PT);
    Qt_MC(:, k) = results.branch(cl_idx, QT);
end

% ??????
BusVm_MC_Mean = mean(BusVm_MC, 2);                                         % ??????????
BusVm_MC_Var = var(BusVm_MC, 0, 2);                                        % ?????????

Pf_MC_Mean = mean(Pf_MC, 2);
Pf_MC_Var = var(Pf_MC, 0, 2);

% ???????
BusVm_MC = BusVm_MC';                                                      % ??ú?????N??nb?У???д?????η???????н????
Pf_MC = Pf_MC';                                                            % ??ú??·?????N??ncl?У???д?????η?????????·????

%% ###############################################################
%% 3. ???????Gram-Charlier???????????
fprintf('??????????????Gram-Charlier???????????...\n');

%% ????????????????
order = 6;                                                                 % ?????????6
dW_b  = zeros(npv+npq+npq, order);                                         % ??????????
dW_r  = zeros(size(dW_b));                                                 % ??????????

dVm_b = zeros(nb, order);                                                  % ?????????????
dVm_r = zeros(size(dVm_b));
dVa_b = zeros(nb, order);
dVa_r = zeros(size(dVa_b));

dPf_b = zeros(ncl, order);                                                 % ?·????й??????????
dPf_r = zeros(size(dPf_b));
dQf_b = zeros(ncl, order);
dQf_r = zeros(size(dQf_b));

%% ???????????
load = mpc.bus(:, PD:QD);                                                  % ???????????
load = [load zeros(size(load,1),4)];                                       % ???????洢???????????

load(:, 3) = load(:, 1) / baseMVA;                                         % ?й??????????EX_P?????????
EX_P = load(:, 3);
load(:, 4) = abs(EX_P .* EDratio);                                         % ?й?????????DX_P?????????
DX_P = load(:, 4);

load(:, 5) = load(:, 2) / baseMVA;                                         % ????????????EX_Q?????????
EX_Q = load(:, 5);
load(:, 6) = abs(EX_Q .* EDratio);                                         % ???????????DX_Q?????????
DX_Q = load(:, 6);

%% ????????????????
if ConnectRE == 1
    % ???????????????????
    for i = 1:num_RE
        % ????INJECT_POWER???????????????????????CSV??????к??
        [INJP_EX, dINJP_r] = INJECT_POWER(n_samples, RE_CSV_Files{i}, RE_CSV_Columns(i));

        % ?????dW_r?е?λ??
        nRE = npv + find(pq==busRE(i));  % nRE????dW_r?е?λ??????PQ???

        % ?й??????????????????????????????
        dP_RE_r_pu = dINJP_r / baseMVA * REPowerRatio(i); % ????????????????????

        fprintf('?????????????????????? %d, ???????: %.2f MW???????й??????\n', busRE(i), INJP_EX * REPowerRatio(i));

        % ???????????????????й???????????м??
        % ??????????????????????
        for j = 1:order
            if j > 1 % ????????????????
                dW_r(nRE, j) = dW_r(nRE, j) - dP_RE_r_pu(j);
                % ????????????
            end
        end
    end
end

%% 注入功率扰动量的统计特性建模
% 假设负荷扰动量服从正态分布，高阶累积量为0
dW_b(:,2) = [DX_P(pv) .^ 2;                                                % 参考newtonpf2.m排序
             DX_P(pq) .^ 2;                                                % DX_P方差
             DX_Q(pq) .^ 2];                                               % DX_Q方差

dW_r = b2r(dW_b);                                                          % b2r函数将中心矩转换为累积量

%% ??????????????????
% ???????????????????????
[results_gc, success, others] = runpf2(mpc, mpt);                          % ????????????????
bus_gc     = results_gc.bus;
branch_gc  = results_gc.branch;
gen_gc     = results_gc.gen;
J0      = others.J0;                                                       % ???????
Sen     = J0 \ eye(size(J0));                                              % ?????????
cl_idx_gc = find(mpc.branch(:,BR_STATUS) == 1);                            % ?????·????
Tpf     = real(others.Gf0) * Sen;                                          % ?·??????????
Tqf     = imag(others.Gf0) * Sen;

% 状态变量累积量计算
for i = 1:order
    dV_r(:,i) = (Sen.^i) * dW_r(:,i);                                      % 状态变量累积量计算
end

% 建立节点电压索引
j1 = 1;         j2 = npv;                                                  % j1:j2 - PV节点相角
j3 = j2 + 1;    j4 = j2 + npq;                                             % j3:j4 - PQ节点相角
j5 = j4 + 1;    j6 = j4 + npq;                                             % j5:j6 - PQ节点电压

dVa_r(pv,:) = dV_r(j1:j2,:);                                               % 相角扰动量通常为0
dVa_r(pq,:) = dV_r(j3:j4,:);
dVm_r(pq,:) = dV_r(j5:j6,:);                                               % PV节点电压扰动为0

% ?????????????
for i = 1:order
	dPf_r(:,i) = (Tpf(cl_idx_gc,:).^i) * dW_r(:,i) .* baseMVA^i;           % ?????????????????????
	dQf_r(:,i) = (Tqf(cl_idx_gc,:).^i) * dW_r(:,i) .* baseMVA^i;
end

% ?????
dVm_b = r2b(dVm_r);                                                        % r2b?????????????????
dVa_b = r2b(dVa_r);
dPf_b = r2b(dPf_r);
dQf_b = r2b(dQf_r);

% ???????
dVm_EX = bus_gc(pq,VM);                                                    % pq??????????
[dVm_pdf, dVm_cdf, dVm_x] = GramCharlierVm2(dVm_b(pq,:), dVm_r(pq,:), dVm_EX);	% pq????????

% ?·?й????????
dPf_EX = branch_gc(cl_idx_gc,PF);                                           % ?????·?й??????????
[dPf_pdf, dPf_cdf, dPf_x] = GramCharlierPf2(dPf_b, dPf_r, dPf_EX);         % ?·?????й???????

%% ###############################################################
%% 4. MC??GC??????
fprintf('??????ж?????...\n');

%% pq节点电压的PDF和CDF对比
% 计算MC方法的PDF和CDF
pqBusVm_MC = BusVm_MC(:,pq);                                               % pq节点电压
xVm = (lowerVm:scaleVm:upperVm)';                                          % 电压横坐标
nx = length(xVm);
xVm_MC = repmat(xVm,1,npq);                                                % 复制为npq列
for j = 1:npq
    xVm_MC(:,j) = xVm_MC(:,j) + BusVm_EX(pq(j));                           % 加偏移
end

% 初始化MC方法结果数组
pdfVm_MC = zeros(nx,npq);
cdfVm_MC = zeros(nx,npq);

% 统计超出范围的样本数量
pdfVm_MC_outOfRange = zeros(1,npq);

for k = 1:npq                                                              % 第k个pq节点
    % 统计超出范围的样本
    outOfRange = sum(pqBusVm_MC(:,k) <= xVm_MC(1,k) | pqBusVm_MC(:,k) >= xVm_MC(end,k));
    if outOfRange > 0
        warning('节点 %d: 有 %d 个样本超出计算范围，可能影响精度', pq(k), outOfRange);
        pdfVm_MC_outOfRange(k) = outOfRange/N;
    end

    for i = 2:nx                                                           % nx-1个区间
        % 统计落在该区间的样本数
        count = sum(xVm_MC(i-1,k) <= pqBusVm_MC(:,k) & pqBusVm_MC(:,k) < xVm_MC(i,k));
        pdfVm_MC(i,k) = count/N/scaleVm;                                   % 概率密度（样本数/总数/区间宽度）
    end

    % 计算累积分布函数
    for i = 1:nx
        cdfVm_MC(i,k) = sum(pqBusVm_MC(:,k) < xVm_MC(i,k))/N;             % 累积概率CDF
    end
end

% 绘制对比图
figure('Name','PQ节点电压的PDF和CDF对比');
% 只绘制指定的节点
for i = 1:length(nodes_to_plot)
    node_idx = nodes_to_plot(i);
    if node_idx <= npq
        % PDF对比
        subplot(length(nodes_to_plot), 2, 2*i-1);
        plot(xVm_MC(:,node_idx), pdfVm_MC(:,node_idx), 'b-', 'LineWidth', 1.5);
        hold on;
        plot(dVm_x(:,node_idx), dVm_pdf(:,node_idx), 'r--', 'LineWidth', 1.5);
        grid on;
        title(['PQ节点 ', num2str(pq(node_idx)), ' 电压幅值PDF']);
        legend('蒙特卡洛', '累积量法');

        % CDF对比
        subplot(length(nodes_to_plot), 2, 2*i);
        plot(xVm_MC(:,node_idx), cdfVm_MC(:,node_idx), 'b-', 'LineWidth', 1.5);
        hold on;
        plot(dVm_x(:,node_idx), dVm_cdf(:,node_idx), 'r--', 'LineWidth', 1.5);
        grid on;
        title(['PQ节点 ', num2str(pq(node_idx)), ' 电压幅值CDF']);
        legend('蒙特卡洛', '累积量法');
    end
end

%% 支路有功功率PDF和CDF对比
% 计算MC方法的PDF和CDF
xPf = (lowerPf:scalePf:upperPf)';                                          % 功率横坐标
nx = length(xPf);
xPf_MC = repmat(xPf,1,ncl);                                                % 复制为ncl列
for j = 1:ncl
    xPf_MC(:,j) = xPf_MC(:,j) + Pf_EX(cl_idx(j));                          % 加偏移
end

% 初始化MC方法结果数组
pdfPf_MC = zeros(nx,ncl);
cdfPf_MC = zeros(nx,ncl);

% 统计超出范围的样本数量
pdfPf_MC_outOfRange = zeros(1,ncl);

for k = 1:ncl                                                              % 第k条支路
    % 统计超出范围的样本
    outOfRange = sum(Pf_MC(:,k) <= xPf_MC(1,k) | Pf_MC(:,k) >= xPf_MC(end,k));
    if outOfRange > 0
        warning('支路 %d: 有 %d 个样本超出计算范围，可能影响精度', cl_idx(k), outOfRange);
        pdfPf_MC_outOfRange(k) = outOfRange/N;
    end

    for i = 2:nx                                                           % nx-1个区间
        % 统计落在该区间的样本数
        count = sum(xPf_MC(i-1,k) <= Pf_MC(:,k) & Pf_MC(:,k) < xPf_MC(i,k));
        pdfPf_MC(i,k) = count/N/scalePf;                                   % 概率密度（样本数/总数/区间宽度）
    end

    % 计算累积分布函数
    for i = 1:nx
        cdfPf_MC(i,k) = sum(Pf_MC(:,k) < xPf_MC(i,k))/N;                  % 累积概率CDF
    end
end

% 绘制对比图
figure('Name','支路有功功率PDF和CDF对比');
% 只绘制指定的支路
for i = 1:length(branches_to_plot)
    branch_idx = branches_to_plot(i);
    if branch_idx <= ncl
        % PDF对比
        subplot(length(branches_to_plot), 2, 2*i-1);
        plot(xPf_MC(:,branch_idx), pdfPf_MC(:,branch_idx), 'b-', 'LineWidth', 1.5);
        hold on;
        plot(dPf_x(:,branch_idx), dPf_pdf(:,branch_idx), 'r--', 'LineWidth', 1.5);
        grid on;
        title(['支路 ', num2str(cl_idx(branch_idx)), ' 有功功率PDF']);
        legend('蒙特卡洛', '累积量法');

        % CDF对比
        subplot(length(branches_to_plot), 2, 2*i);
        plot(xPf_MC(:,branch_idx), cdfPf_MC(:,branch_idx), 'b-', 'LineWidth', 1.5);
        hold on;
        plot(dPf_x(:,branch_idx), dPf_cdf(:,branch_idx), 'r--', 'LineWidth', 1.5);
        grid on;
        title(['支路 ', num2str(cl_idx(branch_idx)), ' 有功功率CDF']);
        legend('蒙特卡洛', '累积量法');
    end
end

%% ????????????
% ??????
Vm_mean_error = abs(BusVm_MC_Mean(pq) - dVm_EX) ./ dVm_EX * 100;
Vm_var_error = abs(BusVm_MC_Var(pq) - dVm_b(pq,2)) ./ dVm_b(pq,2) * 100;

% ?·???????
Pf_mean_error = abs(Pf_MC_Mean - dPf_EX) ./ abs(dPf_EX) * 100;
Pf_var_error = abs(Pf_MC_Var - dPf_b(:,2)) ./ abs(dPf_b(:,2)) * 100;

% ??????
figure('Name','????????????????????? (%)');
subplot(2,2,1);
bar(Vm_mean_error);
title('PQ????????????? (%)');
xlabel('PQ????');
grid on;

subplot(2,2,2);
bar(Vm_var_error);
title('PQ???????????? (%)');
xlabel('PQ????');
grid on;

subplot(2,2,3);
bar(Pf_mean_error);
title('?·?й????????????? (%)');
xlabel('?·??');
grid on;

subplot(2,2,4);
bar(Pf_var_error);
title('?·?й??????????? (%)');
xlabel('?·??');
grid on;

% ?????????????
elapsed_time = toc;
fprintf('?????????????: %.2f ??\n', elapsed_time);

% ????????
fprintf('=====================?????=======================\n');
fprintf('??????巨???????????????\n');
fprintf('1. ?????????????: %d\n', N);
fprintf('2. PQ?????????????: ??? %.2f%%, ??? %.2f%%\n', max(Vm_mean_error), mean(Vm_mean_error));
fprintf('3. PQ????????????: ??? %.2f%%, ??? %.2f%%\n', max(Vm_var_error), mean(Vm_var_error));
fprintf('4. ?·?й?????????????: ??? %.2f%%, ??? %.2f%%\n', max(Pf_mean_error), mean(Pf_mean_error));
fprintf('5. ?·?й???????????: ??? %.2f%%, ??? %.2f%%\n', max(Pf_var_error), mean(Pf_var_error));

% ??????????Χ???????
if any(pdfVm_MC_outOfRange > 0)
    fprintf('\n???????????Χ???????\n');
    for k = 1:npq
        if pdfVm_MC_outOfRange(k) > 0
            fprintf('PQ??? %d: ?? %.2f%% ???????????趨??Χ [%.4f, %.4f]\n', ...
                pq(k), pdfVm_MC_outOfRange(k)*100, xVm_MC(1,k), xVm_MC(end,k));
        end
    end
    fprintf('????lowerVm??upperVm??????????????????Χ\n');
end

if any(pdfPf_MC_outOfRange > 0)
    fprintf('\n?·???????????Χ???????\n');
    for k = 1:ncl
        if pdfPf_MC_outOfRange(k) > 0
            fprintf('?· %d: ?? %.2f%% ???????????趨??Χ [%.4f, %.4f]\n', ...
                cl_idx(k), pdfPf_MC_outOfRange(k)*100, xPf_MC(1,k), xPf_MC(end,k));
        end
    end
    fprintf('????lowerPf??upperPf??????????????????Χ\n');
end

fprintf('======================================================\n');

%% ??????
fprintf('\n=================== ?????? ===================\n');

% pq
fprintf('\n--- pq????? ---\n');
fprintf('%-6s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s\n', '???', 'MC????', 'GC????', '???????(%)', 'MC????', 'GC????', '???????(%)', 'MC?й?', 'GC?й?', '?й????(%)');
fprintf('%-6s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s\n', '------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------');

for i = 1:npq
    fprintf('%-6d | %-15.6f | %-15.6f | %-15.2f | %-15.8e | %-15.8e | %-15.2f | %-15.8e | %-15.8e | %-15.2f\n', ...
        pq(i), ...
        BusVm_MC_Mean(pq(i)), ...
        dVm_EX(i), ...
        Vm_mean_error(i), ...
        BusVm_MC_Var(pq(i)), ...
        dVm_b(pq(i),2), ...
        Vm_var_error(i), ...
        bus_gc(pq(i),VM), ...
        dVm_EX(i));
end

% ?й?????
fprintf('\n--- ?й??????? ---\n');
fprintf('%-6s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s\n', '?·', 'MC?й?', 'GC?й?', '?й????(%)', 'MC?й?????', 'GC?й?????', '?й????(%)', 'MC????', 'GC????', '???????(%)');
fprintf('%-6s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s | %-15s\n', '------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------', '---------------');

for i = 1:ncl
    fprintf('%-6d | %-15.6f | %-15.6f | %-15.2f | %-15.8e | %-15.8e | %-15.2f | %-15.8e | %-15.8e | %-15.2f\n', ...
        cl_idx(i), ...
        Pf_MC_Mean(i), ...
        dPf_EX(i), ...
        Pf_mean_error(i), ...
        Pf_MC_Var(i), ...
        dPf_b(i,2), ...
        Pf_var_error(i), ...
        bus_gc(cl_idx(i),PF), ...
        dPf_EX(i));
end

fprintf('\n======================================================\n');

% ???????
if ResultSave
    % ????????
    PQ_table = table(pq, BusVm_MC_Mean(pq), dVm_EX, Vm_mean_error, ...
        BusVm_MC_Var(pq), dVm_b(pq,2), Vm_var_error, ...
        'VariableNames', {'???', 'MC????', 'GC????', '???????(%)', 'MC????', 'GC????', '???????(%)'});

    Branch_table = table(cl_idx, Pf_MC_Mean, dPf_EX, Pf_mean_error, ...
        Pf_MC_Var, dPf_b(:,2), Pf_var_error, ...
        'VariableNames', {'?·', 'MC?й?', 'GC?й?', '?й????(%)', 'MC?й?????', 'GC?й?????', '?й????(%)'});

    % ???浽Excel
    writetable(PQ_table, 'pq?????.xlsx');
    writetable(Branch_table, '?й???????.xlsx');

    fprintf('???????浽Excel\n');
end