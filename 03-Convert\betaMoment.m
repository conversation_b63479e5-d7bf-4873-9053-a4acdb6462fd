function [ m ] = betaMoment( a, b )
%BETAMOMENT 生成服从Beta分布的随机变量的各阶原点矩，默认生成到6阶
%% 太阳光辐射强度r符合Beta分布------------------------------------%%
% f(r) = gamma(a+b)/(gamma(a)*gamma(b)) * (r/Rmax)^(a-1) * (1-r/Rmax)^(b-1)
% a = 0.79;                                                                 % Beta分布的形状参数
% b = 1.87;                                                                 % Beta分布的形状参数

%% 6阶原点矩生成，参考wiki
% 只用到a和b两参数，所生成的各阶矩对应的是标幺值 或 1MVA
m(1,1) = a ./(a+b);                                                         % 设为列向量
for k = 2:6
	m(k,1) = m(k-1,1) .* ( (a+k-1) ./ (a+b+k-1) );                          % 已通过符号运算验证
end

%% 期望和方差，校验用
% EX = a ./(a+b); 
% DX = sqrt( m(2,1) - m(1,1).^2 );

end