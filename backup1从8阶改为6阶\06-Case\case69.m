function mpc = case69
%CASE69

%% MATPOWER Case Format : Version 2
mpc.version = '2';

%%-----  Power Flow Data  -----%%
%% system MVA base
mpc.baseMVA = 10;

%% bus data
%	bus_i	type	Pd	Qd	Gs	Bs	area	Vm	Va	baseKV	zone	Vmax	Vmin
mpc.bus = [
	1	3	0       0       0	0	1	1	0	12.66	1	1	0.9;
	2	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	3	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	4	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	5	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	6	1	0.0026	0.0022	0	0	1	1	0	12.66	1	1	0.9;
	7	1	0.0404	0.03	0	0	1	1	0	12.66	1	1	0.9;
	8	1	0.075	0.054	0	0	1	1	0	12.66	1	1	0.9;
	9	1	0.03	0.022	0	0	1	1	0	12.66	1	1	0.9;
	10	1	0.028	0.019	0	0	1	1	0	12.66	1	1	0.9;
	11	1	0.145	0.104	0	0	1	1	0	12.66	1	1	0.9;
	12	1	0.145	0.104	0	0	1	1	0	12.66	1	1	0.9;
	13	1	0.008	0.0055	0	0	1	1	0	12.66	1	1	0.9;
	14	1	0.008	0.0055	0	0	1	1	0	12.66	1	1	0.9;
	15	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	16	1	0.0455	0.03	0	0	1	1	0	12.66	1	1	0.9;
	17	1	0.06	0.035	0	0	1	1	0	12.66	1	1	0.9;
	18	1	0.06	0.035	0	0	1	1	0	12.66	1	1	0.9;
	19	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	20	1	0.001	0.0006	0	0	1	1	0	12.66	1	1	0.9;
	21	1	0.114	0.081	0	0	1	1	0	12.66	1	1	0.9;
	22	1	0.0053	0.0035	0	0	1	1	0	12.66	1	1	0.9;
	23	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	24	1	0.028	0.02	0	0	1	1	0	12.66	1	1	0.9;
	25	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	26	1	0.014	0.01	0	0	1	1	0	12.66	1	1	0.9;
	27	1	0.014	0.01	0	0	1	1	0	12.66	1	1	0.9;
	28	1	0.026	0.0186	0	0	1	1	0	12.66	1	1	0.9;
	29	1	0.026	0.0186	0	0	1	1	0	12.66	1	1	0.9;
	30	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	31	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	32	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	33	1	0.014	0.01	0	0	1	1	0	12.66	1	1	0.9;
	34	1	0.0195	0.014	0	0	1	1	0	12.66	1	1	0.9;
	35	1	0.006	0.004	0	0	1	1	0	12.66	1	1	0.9;
	36	1	0.026	0.01855	0	0	1	1	0	12.66	1	1	0.9;
	37	1	0.026	0.01855	0	0	1	1	0	12.66	1	1	0.9;
	38	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	39	1	0.024	0.017	0	0	1	1	0	12.66	1	1	0.9;
	40	1	0.024	0.017	0	0	1	1	0	12.66	1	1	0.9;
	41	1	0.0012	0.001	0	0	1	1	0	12.66	1	1	0.9;
	42	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	43	1	0.006	0.0043	0	0	1	1	0	12.66	1	1	0.9;
	44	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	45	1	0.03922	0.0263	0	0	1	1	0	12.66	1	1	0.9;
	46	1	0.03922	0.0263	0	0	1	1	0	12.66	1	1	0.9;
	47	1	0   	0   	0	0	1	1	0	12.66	1	1	0.9;
	48	1	0.079	0.0564	0	0	1	1	0	12.66	1	1	0.9;
	49	1	0.3847	0.2745	0	0	1	1	0	12.66	1	1	0.9;
	50	1	0.3847	0.2745	0	0	1	1	0	12.66	1	1	0.9;
	51	1	0.0405	0.0283	0	0	1	1	0	12.66	1	1	0.9;
	52	1	0.0036	0.0027	0	0	1	1	0	12.66	1	1	0.9;
	53	1	0.00435	0.0035	0	0	1	1	0	12.66	1	1	0.9;
	54	1	0.0264	0.019	0	0	1	1	0	12.66	1	1	0.9;
	55	1	0.024	0.0172	0	0	1	1	0	12.66	1	1	0.9;
	56	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	57	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	58	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	59	1	0.1     0.072	0	0	1	1	0	12.66	1	1	0.9;
	60	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	61	1	1.244	0.888	0	0	1	1	0	12.66	1	1	0.9;
	62	1	0.032	0.023	0	0	1	1	0	12.66	1	1	0.9;
	63	1	0       0       0	0	1	1	0	12.66	1	1	0.9;
	64	1	0.227	0.162	0	0	1	1	0	12.66	1	1	0.9;
	65	1	0.059	0.042	0	0	1	1	0	12.66	1	1	0.9;
	66	1	0.018	0.013	0	0	1	1	0	12.66	1	1	0.9;
	67	1	0.018	0.013	0	0	1	1	0	12.66	1	1	0.9;
	68	1	0.028	0.02	0	0	1	1	0	12.66	1	1	0.9;
	69	1	0.028	0.02	0	0	1	1	0	12.66	1	1	0.9;
];

%% generator data
%	bus	Pg	Qg	Qmax	Qmin	Vg	mBase	status	Pmax	Pmin	Pc1	Pc2	Qc1min	Qc1max	Qc2min	Qc2max	ramp_agc	ramp_10	ramp_30	ramp_q	apf
mpc.gen = [
	1	0	0	10	-10	1	10	1	10	0	0	0	0	0	0	0	0	0	0	0	0;
];

%% branch data
%	fbus	tbus	r	x	b	rateA	rateB	rateC	ratio	angle	status	angmin	angmax
mpc.branch = [
	1	2	3.11962e-05	7.48708e-05	0	0	0	0	0	0	1	-360	360;
	2	3	3.11962e-05	7.48708e-05	0	0	0	0	0	0	1	-360	360;
	3	4	9.35886e-05	0.000224613	0	0	0	0	0	0	1	-360	360;
	4	5	0.001566049	0.001834336	0	0	0	0	0	0	1	-360	360;
	5	6	0.022835609	0.011629938	0	0	0	0	0	0	1	-360	360;
	6	7	0.023777733	0.01211036	0	0	0	0	0	0	1	-360	360;
	7	8	0.005752577	0.002932442	0	0	0	0	0	0	1	-360	360;
	8	9	0.003075944	0.001566049	0	0	0	0	0	0	1	-360	360;
	9	10	0.051099354	0.016889615	0	0	0	0	0	0	1	-360	360;
	10	11	0.011679852	0.003862088	0	0	0	0	0	0	1	-360	360;
	11	12	0.044385934	0.014668447	0	0	0	0	0	0	1	-360	360;
	12	13	0.064264144	0.021213407	0	0	0	0	0	0	1	-360	360;
	13	14	0.065137638	0.021525369	0	0	0	0	0	0	1	-360	360;
	14	15	0.066011131	0.021812374	0	0	0	0	0	0	1	-360	360;
	15	16	0.012266341	0.004055504	0	0	0	0	0	0	1	-360	360;
	16	17	0.023359705	0.007724176	0	0	0	0	0	0	1	-360	360;
	17	18	0.000293244	9.98278e-05	0	0	0	0	0	0	1	-360	360;
	18	19	0.020439741	0.006757094	0	0	0	0	0	0	1	-360	360;
	19	20	0.013139834	0.004342509	0	0	0	0	0	0	1	-360	360;
	20	21	0.021313235	0.007044099	0	0	0	0	0	0	1	-360	360;
	21	22	0.000873493	0.000287005	0	0	0	0	0	0	1	-360	360;
	22	23	0.009926627	0.003281839	0	0	0	0	0	0	1	-360	360;
	23	24	0.021606479	0.007143927	0	0	0	0	0	0	1	-360	360;
	24	25	0.046719409	0.015442112	0	0	0	0	0	0	1	-360	360;
	25	26	0.019273004	0.006370261	0	0	0	0	0	0	1	-360	360;
	26	27	0.010806359	0.003568844	0	0	0	0	0	0	1	-360	360;
	3	28	0.000274526	0.000673838	0	0	0	0	0	0	1	-360	360;
	28	29	0.003993112	0.009764406	0	0	0	0	0	0	1	-360	360;
	29	30	0.024819686	0.008204597	0	0	0	0	0	0	1	-360	360;
	30	31	0.004379945	0.001447503	0	0	0	0	0	0	1	-360	360;
	31	32	0.021899723	0.007237515	0	0	0	0	0	0	1	-360	360;
	32	33	0.052347201	0.017569692	0	0	0	0	0	0	1	-360	360;
	33	34	0.106566173	0.035226734	0	0	0	0	0	0	1	-360	360;
	34	35	0.091966358	0.030403803	0	0	0	0	0	0	1	-360	360;
	3	36	0.000274526	0.000673838	0	0	0	0	0	0	1	-360	360;
	36	37	0.003993112	0.009764406	0	0	0	0	0	0	1	-360	360;
	37	38	0.006569917	0.007674262	0	0	0	0	0	0	1	-360	360;
	38	39	0.001896728	0.022149292	0	0	0	0	0	0	1	-360	360;
	39	40	0.000112306	0.000131024	0	0	0	0	0	0	1	-360	360;
	40	41	0.045440365	0.05308967	0	0	0	0	0	0	1	-360	360;
	41	42	0.019341636	0.022604757	0	0	0	0	0	0	1	-360	360;
	42	43	0.002558087	0.002982355	0	0	0	0	0	0	1	-360	360;
	43	44	0.00057401	0.000723752	0	0	0	0	0	0	1	-360	360;
	44	45	0.006794529	0.008566473	0	0	0	0	0	0	1	-360	360;
	45	46	5.61531e-05	7.48708e-05	0	0	0	0	0	0	1	-360	360;
	4	47	0.000212134	0.000524096	0	0	0	0	0	0	1	-360	360;
	47	48	0.005309591	0.012996331	0	0	0	0	0	0	1	-360	360;
	48	49	0.01808131	0.044242432	0	0	0	0	0	0	1	-360	360;
	49	50	0.005128653	0.012547106	0	0	0	0	0	0	1	-360	360;
	8	51	0.005790012	0.002951159	0	0	0	0	0	0	1	-360	360;
	51	52	0.020708029	0.00695051	0	0	0	0	0	0	1	-360	360;
	9	53	0.010856273	0.005527964	0	0	0	0	0	0	1	-360	360;
	53	54	0.012665652	0.006451371	0	0	0	0	0	0	1	-360	360;
	54	55	0.017731912	0.009028176	0	0	0	0	0	0	1	-360	360;
	55	56	0.017550975	0.008940827	0	0	0	0	0	0	1	-360	360;
	56	57	0.099203873	0.03329881	0	0	0	0	0	0	1	-360	360;
	57	58	0.048896903	0.016409194	0	0	0	0	0	0	1	-360	360;
	58	59	0.01897976	0.006276673	0	0	0	0	0	0	1	-360	360;
	59	60	0.024089695	0.007312386	0	0	0	0	0	0	1	-360	360;
	60	61	0.031664129	0.016128428	0	0	0	0	0	0	1	-360	360;
	61	62	0.006077017	0.003094662	0	0	0	0	0	0	1	-360	360;
	62	63	0.009046894	0.004604557	0	0	0	0	0	0	1	-360	360;
	63	64	0.044329781	0.0225798	0	0	0	0	0	0	1	-360	360;
	64	65	0.06495046	0.033080436	0	0	0	0	0	0	1	-360	360;
	11	66	0.012553345	0.003812174	0	0	0	0	0	0	1	-360	360;
	66	67	0.000293244	8.73493e-05	0	0	0	0	0	0	1	-360	360;
	12	68	0.046132921	0.015248696	0	0	0	0	0	0	1	-360	360;
	68	69	0.000293244	9.98278e-05	0	0	0	0	0	0	1	-360	360;
	13	20	0.031196187	0.031196187	0	0	0	0	0	0	0	-360	360;
	46	15	0.062392373	0.062392373	0	0	0	0	0	0	0	-360	360;
	50	59	0.124784746	0.124784746	0	0	0	0	0	0	0	-360	360;
	11	43	0.031196187	0.031196187	0	0	0	0	0	0	0	-360	360;
	65	27	0.062392373	0.062392373	0	0	0	0	0	0	0	-360	360;
];

%% DC Line Data
%	fbus	tbus	status	Pf	Pt      Qf	Qt	Vf	Vt	Pmin	Pmax	QminF	QmaxF	QminT	QmaxT	loss0	loss1
mpc.dcline = [
	54     27      1       0.495	0.495	0.5	0.071	1	1	-0.5     0.5     -0.5	0.5     -0.5	0.5     0       0;
];

%%-----  Power Flow Data  -----%%
%% Generator Cost Data
%	1	startup	shutdown	n	x1	y1	...	xn	yn
%	2	startup	shutdown	n	c(n-1)	...	c0

mpc.gencost = [
	2	0	0	2	1	1;
];