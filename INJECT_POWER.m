function [ INJP_EX, dINJP_r, INJP_Sample ] = INJECT_POWER( n, csv_file, data_column )
% INJECT_POWER 可再生能源注入功率数据处理
% 从CSV文件读取数据并计算累积量用于概率潮流分析
%
% 输入参数:
%   n - 仿真次数
%   csv_file - CSV文件路径（如'临城下峪晶澳光伏_96_lstm_预测.csv'）
%   data_column - 数据列号（如第14列为"偏差值"列）
% 输出参数:
%   INJP_EX - 注入功率期望值
%   dINJP_r - 注入功率累积量矩阵
%   INJP_Sample - 注入功率样本数据（用于蒙特卡洛对比）

% 设置字符编码为GBK以正确处理MATLAB中文显示
feature('DefaultCharacterSet', 'GBK');

% 参数检查
if nargin < 1
    error('必须提供参数 n （仿真次数）');
end

% 默认参数
if nargin < 2
    csv_file = '临城下峪晶澳光伏_96_lstm_预测.csv';
end

if nargin < 3
    data_column = 14; % 默认读取第14列（偏差值）
end

% 读取数据
% 使用GBK编码读取
opts = detectImportOptions(csv_file, 'Encoding', 'GBK'); % 设置GBK编码
M = readtable(csv_file, opts); % 读取CSV文件，确保使用GBK编码

% 提取data_column列的数据，跳过标题行，读取96*n行数据
if size(M, 1) >= 96*n+1
    M_data = M{2:96*n+1, data_column};
else
    warning('CSV文件数据行数不足，将重复使用现有数据');
    M_data = M{2:end, data_column};
    % 如果数据不足，重复使用
    if length(M_data) < 96*n
        M_data = repmat(M_data, ceil(96*n/length(M_data)), 1);
        M_data = M_data(1:96*n);
    end
end
INJP_Sample = M_data; % 注入功率样本数据（标幺值）

% 基本统计
maxM = max(M_data);

% 计算期望值和标准差
miuM = mean(M_data);
sitaM = sqrt(var(M_data));

%% 矩计算过程
nMoments = 6; % 计算前6阶矩
dINJP_a = zeros(1, nMoments);

% 计算原始矩
for r = 1:nMoments
    dINJP_a(r) = mean((M_data).^r);
end

% 矩转换：原始矩→中心矩→累积量
dINJP_b = a2b(dINJP_a);
dINJP_r = b2r(dINJP_b);

% 期望值输出
INJP_EX = dINJP_a(1);

% 调用GramCharlierINJP函数进行Gram-Charlier展开分析
% 生成概率分布图并保存GramCharlierINJP系数到Excel
[gc_pdf, gc_cdf, xx] = GramCharlierINJP(dINJP_b, INJP_EX, M_data);

% 输出统计信息
fprintf('CSV文件: %s, 列号: %d (偏差值)\n', csv_file, data_column);
fprintf('最大值: %f\n', maxM);
fprintf('期望值: %f\n', miuM);
fprintf('标准差: %f\n', sitaM);

end
