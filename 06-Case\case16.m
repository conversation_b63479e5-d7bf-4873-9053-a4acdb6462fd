function mpc = case16
%CASE16

%% MATPOWER Case Format : Version 2
mpc.version = '2';

%%-----  Power Flow Data  -----%%
%% system MVA base
mpc.baseMVA = 100;

%% bus data
%	bus_i	type	Pd	Qd	Gs	Bs	area	Vm	Va	baseKV	zone	Vmax	Vmin
mpc.bus = [
	1	3	0	0	0	0	1	1	0	23	1	1	0.9;
	2	3	0	0	0	0	1	1	0	23	1	1	0.9;
	3	3	0	0	0	0	1	1	0	23	1	1	0.9;
	4	1	2	1.6	0	0	1	1	0	23	1	1	0.9;
	5	1	3	1.5	0	0	1	1	0	23	1	1	0.9;
	6	1	2	0.8	0	0	1	1	0	23	1	1	0.9;
	7	1	1.5	1.2	0	0	1	1	0	23	1	1	0.9;
	8	1	4	2.7	0	0	1	1	0	23	1	1	0.9;
	9	1	5	3	0	0	1	1	0	23	1	1	0.9;
	10	1	1	0.9	0	0	1	1	0	23	1	1	0.9;
	11	1	0.6	0.1	0	0	1	1	0	23	1	1	0.9;
	12	1	4.5	2	0	0	1	1	0	23	1	1	0.9;
	13	1	1	0.9	0	0	1	1	0	23	1	1	0.9;
	14	1	1	0.7	0	0	1	1	0	23	1	1	0.9;
	15	1	1	0.9	0	0	1	1	0	23	1	1	0.9;
	16	1	2.1	1	0	0	1	1	0	23	1	1	0.9;
];

%% generator data
%	bus	Pg	Qg	Qmax	Qmin	Vg	mBase	status	Pmax	Pmin	Pc1	Pc2	Qc1min	Qc1max	Qc2min	Qc2max	ramp_agc	ramp_10	ramp_30	ramp_q	apf
mpc.gen = [
	1	0	0	100	-100	1	100	1	100	0	0	0	0	0	0	0	0	0	0	0	0;
	2	0	0	100	-100	1	100	1	100	0	0	0	0	0	0	0	0	0	0	0	0;
	3	0	0	100	-100	1	100	1	100	0	0	0	0	0	0	0	0	0	0	0	0;
];

%% branch data
%	fbus	tbus	r	x	b	rateA	rateB	rateC	ratio	angle	status	angmin	angmax
mpc.branch = [
	1	4	0.075	0.1     0	0	0	0	0	0	1	-360	360;
	4	5	0.08	0.11	0	0	0	0	0	0	1	-360	360;
	4	6	0.09	0.18	0	0	0	0	0	0	1	-360	360;
	6	7	0.04	0.04	0	0	0	0	0	0	1	-360	360;
	2	8	0.11	0.11	0	0	0	0	0	0	1	-360	360;
	8	9	0.08	0.11	0	0	0	0	0	0	1	-360	360;
	8	10	0.11	0.11	0	0	0	0	0	0	1	-360	360;
	9	11	0.11	0.11	0	0	0	0	0	0	1	-360	360;
	9	12	0.08	0.11	0	0	0	0	0	0	1	-360	360;
	3	13	0.11	0.11	0	0	0	0	0	0	1	-360	360;
	13	14	0.09	0.12	0	0	0	0	0	0	1	-360	360;
	13	15	0.08	0.11	0	0	0	0	0	0	1	-360	360;
	15	16	0.04	0.04	0	0	0	0	0	0	1	-360	360;
	5	11	0.04	0.04	0	0	0	0	0	0	1	-360	360;
	10	14	0.04	0.04	0	0	0	0	0	0	1	-360	360;
	7	16	0.09	0.12	0	0	0	0	0	0	1	-360	360;
];
